<template>
  <bf-dialog
    v-model="visible"
    ref="bfSpeaking"
    :title="showNetCalling ? $t('dialog.networkSpeaking') : $t('dispatch.functionList.keyboardDial')"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :modal="false"
    class="header-border shadow-md shadow-slate-800 speaking-dialog drag-dialog"
    modal-class="drag-dialog-modal"
    append-to-body
    @open="openDlgFn"
    draggable
    center
  >
    <div v-if="!showNetCalling">
      <!-- 拨号显示区域 -->
      <div class="mb-4">
        <bf-input v-model="phoneNumber" placeholder="请输入电话号码" clearable />
      </div>

      <!-- 通话限时设置 -->
      <div class="w-full mb-4">
        <div class="text-white text-sm">{{ $t('dialog.maxSpeakTime') }}：</div>
        <bf-input-number-v2 v-model="speakInfo.maxSpeakTime" :min="30" :max="300" :step="10" class="!w-full" />
      </div>

      <!-- 数字键盘 -->
      <div class="keypad-section mb-4">
        <div class="keypad-grid">
          <button v-for="key in keypadKeys" :key="key" class="keypad-button" @click="addDigit(key)">
            {{ key }}
          </button>
        </div>

        <img v-if="speakState.current === 0" src="@/assets/images/dispatch/function_list/broadcast_call.svg" class="w-[76px] h-[100px] mt-[20px]" />
        <img v-else src="@/assets/images/dispatch/function_list/broadcast_call_selected.svg" class="w-[76px] h-[100px] mt-[20px]" />
        <el-tooltip popper-class="bf-tooltip" :content="$t('dialog.switchToNetworkSpeaking')" placement="bottom">
          <p class="join-net-call-btn" @click="switchToNetworkSpeaking"><span class="bf-iconfont bfdx-wangluodianhua text-[#8299A9]"></span></p>
        </el-tooltip>
      </div>
    </div>
    <el-form v-else ref="speakInfoForm" v-model="speakInfo" label-position="left" :label-width="speakInfoFormLabelWidth">
      <el-form-item>
        <template #label>
          <EllipsisText :content="$t('dialog.maxSpeakTime') + '：'" :style="{ height: '50px', lineHeight: '50px' }" />
        </template>
        <bf-input-numberV2 v-model="speakInfo.maxSpeakTime" :min="30" :max="300" :step="10" class="!w-full" />
      </el-form-item>
      <el-form-item>
        <template #label>
          <EllipsisText :content="$t('dialog.speakDevice') + '：'" :style="{ height: '50px', lineHeight: '50px' }" />
        </template>
        <bf-input v-model="speakDevice" readonly />
      </el-form-item>
      <el-form-item>
        <template #label>
          <EllipsisText :content="$t('dialog.callTarget') + '：'" :style="{ height: '50px', lineHeight: '50px' }" />
        </template>
        <bf-input v-model="targetName" readonly class="send-group-append">
          <template #prepend>
            <el-popover
              ref="dynamicGroupMembers"
              popper-class="listenGroupPopover bf-tooltip"
              placement="left"
              :title="$t('dynamicGroup.dynamicGroupMembers')"
              width="200"
              trigger="click"
            >
              <div class="listenGroupList">
                <div v-for="(memberName, idx) in dynamicGroupMemberInfo" :key="memberName" class="listenGroupList-item">
                  {{ `${idx + 1}: ${memberName}` }}
                </div>
              </div>
              <template #reference>
                <el-button class="target-input-btn" :disabled="disableDynamicGroupMember">
                  <span class="bf-iconfont bfdx-tongzhixiaoxi text-[white]"></span>
                </el-button>
              </template>
            </el-popover>
          </template>
          <template #append>
            <el-button class="target-input-btn" @click="createAndFastSpeak">
              <span class="bf-iconfont bfdx-Subtract text-[white]"></span>
            </el-button>
            <el-button class="target-input-btn" @click="selectSpeakTarget">
              <span class="bf-iconfont bfdx-tianjia222 text-[white]"></span>
            </el-button>
          </template>
        </bf-input>
      </el-form-item>
      <el-form-item>
        <template #label>
          <EllipsisText :content="$t('dialog.callBackTarget') + '：'" :style="{ height: '50px', lineHeight: '50px' }" />
        </template>
        <bf-input v-model="callbackTargetName" readonly />
      </el-form-item>
      <el-form-item v-if="phoneNo" class="incoming-call">
        <template #label>
          <EllipsisText :content="$t('dialog.incomingCall') + '：'" :style="{ height: '50px', lineHeight: '50px' }" />
        </template>
        <bf-input v-model="phoneNo" class="incoming-call-phoneNo" />
        <el-button-group class="incoming-call-btns">
          <el-tooltip popper-class="bf-tooltip" effect="dark" :content="$t('dialog.hangup')" placement="bottom">
            <el-button type="danger" class="iconfont icon-hangup" @click="telephoneHangup" />
          </el-tooltip>
          <el-tooltip popper-class="bf-tooltip" effect="dark" :content="$t('dialog.callTransfer')" placement="bottom">
            <el-button type="primary" class="iconfont icon-phone-transfer" @click="initTransferTargetTree" />
          </el-tooltip>
        </el-button-group>
      </el-form-item>
      <el-form-item>
        <template #label>
          <el-button :disabled="disUsbTip" class="bf-form-item-button !h-[50px] w-full">
            <p :class="['usb-icon', disUsbTip ? '' : 'usb-icon-active']"><span class="bf-iconfont bfdx-tubiao23 text-[#8299A9]"></span></p>
            <span class="usb-name" v-text="disUsbTip ? '' : usbDeviceName" />
          </el-button>
        </template>
        <el-button icon="circle-close" class="bf-form-item-button !h-[50px] w-full" :disabled="!speakState.pendingTarget" @click.prevent="cancelPending">
          <p :class="['cancel-hang-icon', callbackTargetName ? 'cancel-hang-icon-active' : '']">
            <span class="bf-iconfont bfdx-biaogeguanbi text-[#8299A9]"></span>
          </p>
          <span>{{ $t('dialog.cancelPending') }}</span>
        </el-button>
      </el-form-item>
      <el-form-item label-width="0" class="center speak-microphone">
        <div class="animate-speak-box">
          <el-tooltip popper-class="bf-tooltip" :disabled="canSpeaking" :content="$t('msgbox.firstConnectTC918')" placement="bottom">
            <el-button ref="speakBtn" class="animate-speak-btn" :type="speakBtnType" :disabled="!canSpeaking" @click.prevent="toggleSpeaking">
              <!-- <img v-for="(item, index) in speakImages" v-show="index == speakImagesIndex" :key="index" :src="item" class="animate-speak-image" /> -->
              <img v-if="speakState.current === 0" src="@/assets/images/dispatch/function_list/broadcast_call.svg" />
              <img v-else src="@/assets/images/dispatch/function_list/broadcast_call_selected.svg" />
            </el-button>
          </el-tooltip>
        </div>
        <span class="speak-prompt" v-text="$t('dialog.speakPrompt')" />

        <el-tooltip popper-class="bf-tooltip" :content="$t('dialog.switchToKeypadDialing')" placement="bottom">
          <p class="join-key-dialing-btn" @click="switchToKeypadDialing"><span class="bf-iconfont bfdx-bohao text-[#1398E9]"></span></p>
        </el-tooltip>
      </el-form-item>
    </el-form>

    <!-- 选择通话目标对话框 -->
    <bf-dialog
      v-model="showTargetTree"
      :title="groupTreeTitle"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :fullscreen="fullscreen"
      append-to-body
      width="420px"
      class="header-border speak-target-dialog"
      @close="groupTreeConfirmFunc"
      center
    >
      <dialog-table-tree class="!w-[320px] h-full m-auto" :defaultCheckKeys="[speakInfo.target]" @checkbox-change="checkboxChange"></dialog-table-tree>
    </bf-dialog>
  </bf-dialog>
</template>

<script>
  import { defineAsyncComponent, h, computed } from 'vue'
  import { DbOrgIsVirtual, DynamicGroupState } from '@/utils/dynamicGroup/api'
  import { debounce } from 'lodash'
  import dbCmd from '@/modules/protocol/db.pb.cmd'
  import bfProcess from '@/utils/bfprocess'
  import bfTree, { addDynamicGroupNode, defaultTreeId, isRootNode, removeNode, updateOneOrgNodeTitle } from '@/utils/bftree'
  import bfutil, { deferred } from '@/utils/bfutil'
  import dynamicGroupTree from '@/utils/dynamicGroup/dynamicGroupMixin'
  import bfNotify from '@/utils/notify'
  import qWebChannelObj from '@/utils/qWebChannelObj'
  import vueMixin from '@/utils/vueMixin'
  import eventBus from '@/utils/eventBus'
  import { useRouteParams } from '@/router'
  import { ElButton } from 'element-plus'
  import bfDialog from '@/components/bfDialog/main'
  import bfInputNumberV2 from '@/components/bfInputNumber/main'
  import bfInput from '@/components/bfInput/main'
  import EllipsisText from '@/components/common/EllipsisText.vue'
  import {
    speakInfo,
    speakState,
    SpeakState,
    cmdAgentSpeakDevice,
    cmdAgentExLisGroup,
    listenGroupInfo,
    dynamicGroup,
    dynamicGroupMemberInfo,
    setupTarget,
    speakDevice,
    listenGroupName,
    targetName,
    callbackTargetName,
    initSpeakInfo,
    setSpeakTarget,
    setSpeakListenGroup,
    compareListenGroupChanged,
    updateUserVoipSpeakInfo,
    setSpeakState,
    setPendingTarget,
    cleanPendingTarget,
    resumeTarget,
    cleanTempListenGroup,
    setTempListenGroup,
    globalVoipServerManager,
  } from '@/utils/speak'

  const listenGroupTreeNodeOption = {
    showCounter: false,
  }

  const recordIconCtx = import.meta.glob(['@/images/network/record_icon_*.png'], {
    eager: true,
  })
  const { setRouteParams } = useRouteParams()

  export default {
    name: 'BfSpeaking',
    components: {
      bfInput,
      EllipsisText,
      dialogTableTree: defineAsyncComponent(() => import('@/components/common/dialogTableTree.vue')),
      bfDialog,
      bfInputNumberV2,
    },
    mixins: [dynamicGroupTree, vueMixin],
    setup() {
      // 使用全局 VoipServerManager 实例
      const voipServerManager = globalVoipServerManager

      // 暴露从 speak.ts 导入的响应式变量给模板
      return {
        speakInfo,
        speakState,
        SpeakState,
        cmdAgentSpeakDevice,
        cmdAgentExLisGroup,
        listenGroupInfo,
        dynamicGroup,
        dynamicGroupMemberInfo,
        setupTarget,
        speakDevice,
        listenGroupName,
        targetName,
        callbackTargetName,
        initSpeakInfo,
        setSpeakTarget,
        setSpeakListenGroup,
        compareListenGroupChanged,
        updateUserVoipSpeakInfo,
        setSpeakState,
        setPendingTarget,
        cleanPendingTarget,
        resumeTarget,
        cleanTempListenGroup,
        setTempListenGroup,
        voipServerManager,
        // 暴露 VoipServerManager 的响应式属性
        phoneNo: computed(() => voipServerManager.phoneNumber),
        hasUsbDevice: computed(() => voipServerManager.hasUsb),
        usbDeviceName: computed(() => voipServerManager.usbName),
        voipServerConnected: computed(() => voipServerManager.isConnected),
      }
    },
    data() {
      const targetTreeId = 'voipTargetTree'
      const sendGroupTreeId = 'sendGroupTree'

      return {
        visible: false,

        // 控制显示模式：false显示键盘拨号，true显示联网通话
        showNetCalling: false,

        // DialPad 相关数据
        phoneNumber: '',

        targetTreeId,
        showTargetTree: false,
        targetTreeReady: deferred(),
        sendGroupTreeId,
        listenGroupTreeReady: deferred(),
        // selectNodeTarget：1为通话目标，2为接收组，3为电话转接
        selectNodeTarget: 0,
        connectCount: 0,

        // 动画
        speakImages: [...Object.values(recordIconCtx).map(item => item.default)],
        speakImagesIndex: 0,
        speakBtnType: 'primary',

        setKeysEvent: false,
        transferTargetTemp: '',
        transferTarget: '',
        phoneTransferBc15: {},
      }
    },
    methods: {
      // 设置通话目标
      checkboxChange(row) {
        setSpeakTarget(row.rid)
      },
      // 切换到键盘拨号
      switchToKeypadDialing() {
        this.showNetCalling = false
      },

      // 切换到联网通话
      switchToNetworkSpeaking() {
        this.showNetCalling = true
      },

      // DialPad 相关方法
      addDigit(digit) {
        this.phoneNumber += digit
      },
      // 设备树或动态组添加成功后快速启动联网通话，并尝试发起通话
      async speakFast(target) {
        // 无通话目标
        if (!target) {
          return
        }

        // 通话目标不应该为自己
        if (target === speakInfo.speaker) {
          bfNotify.messageBox(this.$t('msgbox.targetNotSelf'), 'warning')
          return
        }

        const awaitVoipRegister = () => {
          return new Promise(resolve => {
            if (this.voipServerConnected) {
              return resolve()
            }

            // 未初始化TC918，订阅连接事件
            eventBus.once('sg_voip_register_ok', () => {
              this.$nextTick(resolve)
            })
          })
        }
        await awaitVoipRegister()

        speakInfo.target = target

        // 在下一事件循环处理，以便通话目标可以正确设置
        setTimeout(() => {
          // 不能进行通话，可能没有指挥坐席等
          if (!this.canSpeaking) {
            return
          }
          // 讲话状态，0 结束讲话，1 自己在讲话，2 他人在讲话
          // 可以抢话语权
          if (speakState.current === 1) {
            // 开始通话前要先结束当前通话
            globalVoipServerManager.sl_i_speak_end()
          }

          globalVoipServerManager.sl_i_speak_start()
        }, 0)
      },
      createAndFastSpeak() {
        // 先解绑上一次的订阅事件
        if (this.lastCreateFastDynamicGroupSubject) {
          bfglob.off(this.lastCreateFastDynamicGroupSubject)
        }
        const subject = (this.lastCreateFastDynamicGroupSubject = 'create-dynamic-group-' + Date.now())
        bfglob.once(subject, target => {
          speakInfo.target = target
          this.lastCreateFastDynamicGroupSubject = undefined
        })
        const params = {
          fastCreateDynamicGroup: true,
          routeName: this.$route.name,
          reply: subject,
        }
        // 路由跳转到动态组页面，以创建动态组
        // 判断当前路由是否为动态组，不能跳转到当前路由,会报"NavigationDuplicated"错误
        if (this.$route.name === 'dynamicGroup') {
          // 已经是动态组页面,直接发布事件
          bfglob.emit('create-fast-temp-group', params)
        } else {
          setRouteParams('dynamicGroup', params)
          this.$router.replace({ name: 'dynamicGroup' })
        }
      },
      targetTreeLoaded() {
        this.loadSpeakerTargetNodes()
          .then(() => {
            this.targetTreeReady.resolve(true)
          })
          .catch(() => {})
      },
      listenGroupTreeLoaded() {
        const afterLoaded = () => {
          this.disableExpiredDynamicGroup(this.sendGroupTreeId)
          this.keepSelectNode()
          this.listenGroupTreeReady.resolve(true)
        }
        const orgList = bfutil.objToArray(bfglob.gorgData.getAll())
        const source = bfTree.createTreeOrgSource(orgList, {
          ...listenGroupTreeNodeOption,
          selected: false,
        })
        bfTree.addOrgNodeToTree(this.sendGroupTreeId, source)
        // 确保树节点已经生成再处理后续逻辑
        this.$nextTick(afterLoaded)
      },
      selectNodes(event, data) {
        const selectKeys = $.map(data.tree.getSelectedNodes(), function (node) {
          return node.key
        })
        this.setSpeakInfo(selectKeys)
      },
      clickNode(event, data) {
        const excludeList = ['expander', 'prefix', 'checkbox']
        if (excludeList.includes(data.targetType)) {
          return true
        }
        const node = data.node
        if (!node) {
          return false
        }
        node.setActive()
        node.setSelected(!node.isSelected())
      },

      initKeysEvent() {
        this.$nextTick(() => {
          if (this.$el.firstElementChild) {
            this.setKeysEvent = true
            let isKeyDown = false
            document.onkeydown = function (e) {
              // bfglob.console.log("onkeydown", e.keyCode, this);
              if (isKeyDown) {
                return
              }
              isKeyDown = true
              switch (e.keyCode) {
                case 86:
                  // V
                  if (!e.ctrlKey) {
                    return
                  }
                  bfglob.emit('onkeydownStartSpeak')
                  break
                case 113:
                  // F2
                  bfglob.emit('onkeydownStartSpeak')
                  break
                // 如果是空格或回车，则阻止默认事件
                case 32:
                  //e.preventDefault()
                  if (document.activeElement.nodeName === 'INPUT') {
                    return
                  }
                  bfglob.emit('onkeydownStartSpeak')
              }
            }
            document.onkeyup = function (e) {
              // bfglob.console.log("onkeyup", e.keyCode, this);
              isKeyDown = false
              switch (e.keyCode) {
                case 86:
                  // V
                  if (!e.ctrlKey) {
                    return
                  }
                  bfglob.emit('onkeyupEndSpeak')
                  break
                case 113:
                  // F2
                  bfglob.emit('onkeyupEndSpeak')
                  break
                // 如果是空格或回车，则阻止默认事件
                case 13:
                case 32:
                  if (document.activeElement.nodeName === 'INPUT') {
                    return
                  }
                  bfglob.emit('onkeyupEndSpeak')
              }
            }
          }
        })
      },
      openDlgFn() {
        // this.initSpeakInfo()
        // qWebChannelObj.initServer()
      },
      groupTreeConfirmFunc() {
        switch (this.selectNodeTarget) {
          case 1:
            // 通话目标
            break
          case 2:
            // 接收组
            break
          case 3:
            // 电话转接
            this.transferTarget = this.transferTargetTemp
            this.transferTargetTemp = ''

            // 发送电话转接指令
            this.slPhoneTransfer()
            break
        }
      },
      keepSelectNode() {
        let target = []
        let treeId = this.targetTreeId
        switch (this.selectNodeTarget) {
          case 1:
            // 通话目标
            const key = speakInfo.target

            // 判断是否为全呼
            if (key === bfglob.fullCallDmrId) {
              target.push(key)
              break
            }

            const orgItem = bfglob.gorgData.getDataByIndex(key)
            if (orgItem) {
              target.push(orgItem.rid)
            } else {
              const device = bfglob.gdevices.getDataByIndex(key)
              if (device) {
                target.push(device.rid)
              }
            }
            break
          case 2:
            // 接收组
            treeId = this.sendGroupTreeId
            target = speakInfo.listenGroup.map(key => {
              const orgItem = bfglob.gorgData.getDataByIndex(key)
              if (orgItem) {
                return orgItem.rid
              }
            })
            break
          case 3:
            // 电话转接
            const transferTargetOrg = bfglob.gorgData.getDataByIndex(this.transferTarget)
            if (transferTargetOrg) {
              target.push(transferTargetOrg.rid)
            } else {
              const transferTargetDevice = bfglob.gdevices.getDataByIndex(this.transferTarget)
              if (transferTargetDevice) {
                target.push(transferTargetDevice.rid)
              }
            }
            break
        }

        // 清空过滤结果
        this.$refs[treeId]?.clearFilter()
        this.$refs[treeId]?.selectAll(false)

        let firstSelectNode
        for (let i = 0; i < target.length; i++) {
          const rid = target[i]
          const treeNode = bfTree.getTreeNodeByRid(treeId, rid)
          if (treeNode) {
            treeNode.setSelected(true)
            if (!firstSelectNode) {
              firstSelectNode = treeNode
            }
          }
        }

        if (firstSelectNode) {
          // 如果已经聚焦，则先失去焦点，否则无法跳转到节点
          if (firstSelectNode.isActive()) {
            firstSelectNode.setActive(false)
          }
          firstSelectNode.setActive(true)
          firstSelectNode.setFocus()
        }
      },
      disableSelfTarget() {
        const speakerDevice = bfglob.gdevices.getDataByIndex(speakInfo.speaker)
        if (!speakerDevice) {
          return
        }
        this.$refs[this.targetTreeId]?.setNodeSelected(speakerDevice.rid, false)
        this.$refs[this.targetTreeId]?.unselectableNodeByKey(speakerDevice.rid, true)
      },
      disableFullCall() {
        this.$refs[this.targetTreeId]?.unselectableNodeByKey(bfglob.fullCallDmrId, !bfglob.userInfo.setting.fullCallPerm)
      },
      disableExpiredDynamicGroup(treeId) {
        bfglob.gorgData
          .getDynamicGroup()
          .filter(item => item.orgIsVirtual === DbOrgIsVirtual.TaskGroup && item.dynamicGroupState === DynamicGroupState.Expired)
          .forEach(item => {
            this.$refs[treeId]?.setNodeSelected(item.rid, false)
            this.$refs[treeId]?.unselectableNodeByKey(item.rid, true)
          })
      },
      disableSpecifiedNodes() {
        // 如果没有全呼权限，则禁止选择全呼目标
        this.disableFullCall()
        // 通话目标不能选择自己
        this.disableSelfTarget()
        // 禁用失效的任务组
        this.disableExpiredDynamicGroup(this.targetTreeId)
      },
      async loadSpeakerTargetNodes() {
        await this.$refs[this.targetTreeId]?.toDictTree(defaultTreeId, dict => {
          const extraData = dict.data || {}
          // 如果是虚拟单位下的节点，则过滤掉
          if (!extraData.isOrg && extraData.virtual) {
            return false
          }
          // 过滤掉动态组下成员
          return isRootNode(dict) || !extraData.isDynamicGroupMember
        })

        // 添加一个全呼通讯录节点
        bfTree.addFullCallNode(this.targetTreeId)
        this.disableSpecifiedNodes()
      },
      initTransferTargetTree() {
        this.selectNodeTarget = 3
        this.showTargetTree = true
        this.targetTreeReady.then(() => {
          if (this.selectNodeTarget !== 3) return
          this.keepSelectNode()
        })
      },
      selectSpeakTarget() {
        this.selectNodeTarget = 1
        this.showTargetTree = true
        this.targetTreeReady.then(() => {
          if (this.selectNodeTarget !== 1) return
          this.keepSelectNode()
        })
      },
      setSpeakInfo(selectKeys) {
        switch (this.selectNodeTarget) {
          case 1:
            // 设置通话目标
            this.setSpeakTarget(selectKeys)
            break
          case 3:
            // 设置电话转接目标
            this.setTransferTarget(selectKeys)
            break
        }
      },
      // initSpeakInfo() {
      //   initSpeakInfo()
      // },
      initKeyBoardEvents() {
        // 系统电脑讲话快捷键功能操作
        bfglob.on('onkeydownStartSpeak', () => {
          clearTimeout(this.endSpeakTimes)
          // 如果正在通话中或者麦克风按钮被禁或者是其他人员在讲话，则停止开始讲话指令
          if (!this.canSpeaking || speakState.current !== 0) {
            return
          }
          globalVoipServerManager.sl_i_speak_start()
        })
        bfglob.on('onkeyupEndSpeak', () => {
          // 如果已结束通话，则停止指令
          if (!this.canSpeaking || speakState.current !== 1) {
            return
          }
          // 设置timeout定时器防止抖动误操作结束讲话
          this.endSpeakTimes = setTimeout(() => {
            globalVoipServerManager.sl_i_speak_end()
          }, 300)
        })
      },

      init() {
        // this.getVoipServerInfo();
        // 先设置默认参数
        // initSpeakInfo() // 移除这里的调用，改为在CommunicationDispatch加载完成后调用
        this.initKeyBoardEvents()
      },

      sl_i_speak_start() {
        this.voipServerManager.sl_i_speak_start()
      },
      sl_i_speak_end() {
        this.voipServerManager.sl_i_speak_end()
      },
      sg_local_speaking_end() {
        this.setEndSpeakState()
        // 清除检测本地讲话定时器
        clearInterval(this.localRecordingTimer)

        this.checkCallSessionShouldExpired()
      },
      checkCallSessionShouldExpired(timeout) {
        this.voipServerManager.checkCallSessionShouldExpired(timeout)
      },
      sl_updateListenGroup(_listenGroup = speakInfo.listenGroup) {
        this.voipServerManager.sl_updateListenGroup(_listenGroup)
      },
      sl_setMaxSpeakingTime() {
        this.voipServerManager.updateMaxSpeakingTime()
      },
      sl_setupTargetDmrid() {
        this.voipServerManager.sl_setupTargetDmrid()
      },

      // 通话逻辑
      setStartSpeakState() {
        setSpeakState(1)
      },
      setEndSpeakState() {
        setSpeakState(0)
      },
      setOtherSpeakState() {
        setSpeakState(2)
      },
      toggleSpeaking() {
        if (speakState.current === 1) {
          globalVoipServerManager.sl_i_speak_end()
        } else {
          globalVoipServerManager.sl_i_speak_start()
        }
      },
      cancelPending() {
        // 取消挂起
        this.resumeTarget()
      },
      // 这些方法现在使用从 speak.ts 导入的函数
      // cleanPendingTarget 和 resumeTarget 已经在 setup() 中暴露
      resumeOtherSpeakingStatus() {
        if (this.otherSpeaking) {
          this.setOtherSpeakState()
        } else {
          this.setEndSpeakState()
        }
      },

      // 动画方法
      animateNextImage(i, len) {
        this.speakImagesIndex = i % len
        this.animateTimer = setTimeout(() => {
          this.animateNextImage(++i, len)
        }, 200)
      },
      startAnimate() {
        clearTimeout(this.animateTimer)
        this.animateNextImage(0, this.speakImages.length)
      },
      endAnimate() {
        clearTimeout(this.animateTimer)
        this.speakImagesIndex = 0
      },
      startSpeakStyle() {
        this.speakBtnType = 'danger'
        this.startAnimate()
      },
      endSpeakStyle() {
        this.speakBtnType = 'primary'
        this.endAnimate()
      },
      otherSpeakStyle() {
        this.speakBtnType = 'success'
        this.startAnimate()
      },
      // todo: remove this method,vxetreemanager will watch updateDeviceNodeTitle event
      updateTreeNode(device) {
        bfTree.updateDeviceNodeTitle(this.targetTreeId, device)
      },
      // 设置的组呼通话目标不在收听组列表中，需要临时添加，以便接收目标的回话
      setTempListenGroupLocal() {
        // 跳过单呼目标
        const device = bfglob.gdevices.getDataByIndex(speakInfo.target)
        if (device) {
          return
        }
        // 组呼目标已存在收听组
        if (speakInfo.listenGroup.includes(speakInfo.target)) {
          return
        }

        // // 判断是否已经处理过临时收听组
        // if (this.tempListenGroup === speakInfo.target) {
        //   return
        // }

        this.tempListenGroup = speakInfo.target
        const tempListenGroup = [...speakInfo.listenGroup, speakInfo.target]
        // 添加临时的收听组目标
        globalVoipServerManager.sl_updateListenGroup(tempListenGroup)
      },
      updateUserVoipSpeakInfoLocal() {
        const skInfo = {}
        Object.assign(skInfo, speakInfo)
        skInfo.listenGroup = speakInfo.listenGroup.filter(item => !this.cmdAgentExLisGroup.includes(item))
        bfglob.userInfo.setting.voipSpeakInfo = skInfo
        bfglob.userInfo.setting.ispUdateVoipSpeakInfo = true
        bfProcess.updateUserSetting(JSON.stringify(bfglob.userInfo.setting), dbCmd.DB_USER_PUPDATE)
      },

      // 电话挂断和转接事件逻辑
      // 电话挂断
      telephoneHangup() {
        this.voipServerManager.telephoneHangup()
      },
      // 电话转接
      setTransferTarget(selectKeys) {
        let target = ''

        // 读取选中节点的目标dmrId
        const key = selectKeys.shift() || ''
        const orgItem = bfglob.gorgData.get(key)
        if (orgItem) {
          target = orgItem.dmrId || ''
        } else {
          const device = bfglob.gdevices.get(key)
          if (device) {
            target = device.dmrId || ''
          }
        }

        this.transferTargetTemp = target
      },
      slPhoneTransfer() {
        if (!this.voipServer || !this.transferTarget) {
          return
        }
        this.voipServer.sl_phone_transfer(this.phoneTransferBc15.phoneDmrId, this.phoneTransferBc15.targetDmrId, this.transferTarget)
      },
      // 设置来电显示号码
      setPhoneNo(phoneNo = '') {
        this.voipServerManager.setPhoneNo(phoneNo)
        this.phoneNumber = this.voipServerManager.phoneNumber
      },
      savePhoneTransferBc15(bc15_obj) {
        const hexsourceDmrid = bfutil.uint32DmrId2Hex(bc15_obj.sourceDmrid)
        const hexTargetDmrid = bfutil.uint32DmrId2Hex(bc15_obj.targetDmrid)
        this.phoneTransferBc15.phoneDmrId = hexsourceDmrid
        this.phoneTransferBc15.targetDmrId = hexTargetDmrid

        this.phoneTransferBc15.origin = bc15_obj
      },
      // 给本中心的来电开始/结束指令处理逻辑
      processGatewayTelephone(bc15_obj, _device) {
        this.savePhoneTransferBc15(bc15_obj)
        // 0：语音结束, 1：语音开始
        if (bc15_obj.callStatus === 1) {
          this.setPhoneNo(bc15_obj.phoneNo)
        }
      },

      addOneDeviceNode(device) {
        bfTree.addOneDeviceNode(this.targetTreeId, device, { selected: false })
        bfTree.renderOrgCounter(this.targetTreeId)
        bfTree.renderOrgCounter(this.sendGroupTreeId)
      },
      delOneDeviceNode(device) {
        bfTree.delOneDeviceNode(this.targetTreeId, device)
        bfTree.renderOrgCounter(this.targetTreeId)
        bfTree.renderOrgCounter(this.sendGroupTreeId)
      },
      updateOneDeviceNode(device) {
        bfTree.updateOneDeviceNode(this.targetTreeId, device)
        bfTree.renderOrgCounter(this.targetTreeId)
        bfTree.renderOrgCounter(this.sendGroupTreeId)
      },
      addOneOrgNode(orgData) {
        bfTree.addOneOrgNode(this.targetTreeId, orgData, { selected: false })
        bfTree.renderOrgCounter(this.targetTreeId)
        bfTree.addOneOrgNode(this.sendGroupTreeId, orgData, {
          ...listenGroupTreeNodeOption,
          selected: false,
        })
      },
      delOneOrgNode(key) {
        bfTree.delOneOrgNode(this.targetTreeId, key)
        bfTree.renderOrgCounter(this.targetTreeId)
        bfTree.delOneOrgNode(this.sendGroupTreeId, key)
        // 删除已经选中的接收组数据
        // 本地单位数据已经被删除，只能通过接收组数据遍历查找不存在对应单位数据的dmrId，然后再删除并更新
        const delDmrIdList = speakInfo.listenGroup.filter(dmrId => {
          return !bfglob.gorgData.getDataByIndex(dmrId)
        })
        while (delDmrIdList.length) {
          const dmrId = delDmrIdList.shift()
          const index = speakInfo.listenGroup.findIndex(k => k === dmrId)
          if (index > -1) {
            speakInfo.listenGroup.splice(index, 1)
          }
        }
        // 删除了目标终端的上级，则需要清除选中目标
        const targetData = bfglob.gdevices.getDataByIndex(speakInfo.target)
        if (targetData && targetData.orgId === key) {
          speakInfo.target = ''
        }
        // 删除了坐席终端的上级，则需要清除坐席终端数据
        const speakerData = bfglob.gdevices.getDataByIndex(speakInfo.speaker)
        if (speakerData && speakerData.orgId === key) {
          speakInfo.speaker = ''
        }
      },
      updateOneOrgNode(orgData) {
        bfTree.updateOneOrgNode(this.targetTreeId, orgData)
        bfTree.renderOrgCounter(this.targetTreeId)
        bfTree.updateOneOrgNode(this.sendGroupTreeId, orgData, listenGroupTreeNodeOption)
      },
      treeSortChildren() {
        bfTree.sortChildren(this.targetTreeId)
        bfTree.sortChildren(this.sendGroupTreeId)
      },
      // 动态组及成员操作
      addDynamicGroupNode(source) {
        addDynamicGroupNode(this.targetTreeId, source, { selected: false })
        addDynamicGroupNode(this.sendGroupTreeId, source, { selected: false })
      },
      updateDynamicGroupNode(source) {
        updateOneOrgNodeTitle(this.targetTreeId, source)
        updateOneOrgNodeTitle(this.sendGroupTreeId, source)
      },
      deleteDynamicGroupNode(source) {
        removeNode(this.targetTreeId, source.rid)
        removeNode(this.sendGroupTreeId, source.rid)

        // 同步通话目标和接收组
        if (source.dmrId === speakInfo.speaker) {
          speakInfo.speaker = ''
        }
        // 重置呼叫目标
        if (source.dmrId === speakInfo.target) {
          speakInfo.target = ''
          // // 判断上次呼叫的目标是否存在，存在则使用上次的目标
          // if (bfglob.gorgData.getDataByIndex(lastTarget) || bfglob.gorgData.getDataByIndex(lastTarget)) {
          //     speakInfo.target = lastTarget
          // }else{
          //   speakInfo.target = ''
          // }
        }
        speakInfo.listenGroup = speakInfo.listenGroup.filter(item => item !== source.dmrId)
      },
      gotDbDeviceUpdate(dbDevice) {
        // 判断该终端是否是自己的指挥座席
        if (dbDevice.dmrId !== speakInfo?.speaker) {
          return
        }
        speakInfo.speaker = ''
        this.$nextTick(() => {
          speakInfo.speaker = dbDevice.dmrId
          globalVoipServerManager.sl_updateListenGroup()
          this.updateUserVoipSpeakInfo()
        })
      },
    },
    watch: {
      voipServerConnected(val) {
        bfglob.emit('voipServerConnected', val)
      },
      visible(_val) {
        !this.setKeysEvent && this.initKeysEvent()
      },
      'speakState.current'(val) {
        switch (val) {
          case 1:
            this.startSpeakStyle()
            break
          case 2:
            this.otherSpeakStyle()
            break
          case 0:
          default:
            this.endSpeakStyle()
            break
        }
        bfglob.emit('sgSpeakingType', this.speakBtnType)
      },
      'speakInfo.speaker'(_val) {
        globalVoipServerManager.updateUserInfo()
      },
      'speakInfo.priority'(_val) {
        globalVoipServerManager.updateUserInfo()
      },
      'speakInfo.target'(_val) {
        globalVoipServerManager.updateTargetDmrid()
        // setTempListenGroup()
        updateUserVoipSpeakInfo()
      },
      setupTarget(_val) {
        globalVoipServerManager.updateTargetDmrid()
      },
      'speakInfo.maxSpeakTime'(_val) {
        globalVoipServerManager.updateMaxSpeakingTime()
        updateUserVoipSpeakInfo()
      },
      'speakInfo.listenGroup': {
        deep: true,
        handler(listenGroup, oldListenGroup) {
          if (compareListenGroupChanged(listenGroup, oldListenGroup)) {
            // setTempListenGroup()
            globalVoipServerManager.sl_updateListenGroup()
            updateUserVoipSpeakInfo()
          }
        },
      },
    },
    computed: {
      // DialPad 相关计算属性
      keypadKeys() {
        return ['1', '2', '3', '4', '5', '6', '7', '8', '9', '*', '0', '#']
      },
      disableDynamicGroupMember() {
        return !this.dynamicGroup
      },
      treeOption() {
        return {
          selectMode: 2,
        }
      },
      filterOption() {
        return {
          leavesOnly: false,
        }
      },
      targetTreeOption() {
        return {
          selectMode: 1,
        }
      },
      contextmenuOption() {
        return {
          menu: [
            {
              title: this.$t('tree.online'),
              cmd: 'displayOnline',
            },
            {
              title: this.$t('tree.displayAllDev'),
              cmd: 'displayAll',
            },
          ],
          select: (event, ui) => {
            switch (ui.cmd) {
              case 'displayOnline':
                this.$refs[this.targetTreeId]?.showOnlineDevices()
                break
              case 'displayAll':
                this.$refs[this.targetTreeId]?.showAllDevices()
            }
          },
        }
      },
      voipServer() {
        return qWebChannelObj && qWebChannelObj.server
      },
      canSpeaking() {
        return this.voipServer && speakInfo.speaker && setupTarget.value && this.hasUsbDevice && this.voipServerConnected
      },
      disUsbTip() {
        return !this.hasUsbDevice || !this.voipServerConnected
      },
      groupTreeTitle() {
        let title = ''
        switch (this.selectNodeTarget) {
          case 1:
            // 通话目标
            title = this.$t('dialog.selectCallTarget')
            break
          case 2:
            // 接收组
            title = this.$t('dialog.selectListenGroup')
            break
          case 3:
            // 电话转接
            title = this.$t('dialog.initTransferTargetTree')
            break
        }
        return title
      },
      speakInfoFormLabelWidth() {
        return this.isFR ? '140px' : this.isEN ? '120px' : '120px'
      },
    },
    mounted() {
      bfglob.vspeaking = this
      this.updateUserVoipSpeakInfo = debounce(updateUserVoipSpeakInfo, 500)
      this.init()

      bfglob.on('bc15_speaker_pending', (bc15_obj, device) => {
        switch (bc15_obj.soundType) {
          case 0:
            // 0:默认的正常手台语音申请
            break
          case 1:
            // 1:手台电话会话语音
            break
          case 2:
            // 电话网关过来的开始/结束指令
            // 过滤不是打入本中心坐席的电话
            const hexDmrId = bfutil.uint32DmrId2Hex(bc15_obj.targetDmrid)
            if (hexDmrId !== speakInfo.speaker) {
              return
            }

            this.processGatewayTelephone(bc15_obj, device)
            break
        }
      })

      bfglob.on('bc15_call_timeout', device => {
        if (device.dmrId === speakInfo.speaker) {
          this.setEndSpeakState()
        }
      })
      bfglob.on('reset_voipSpeakInfo_speaker', (speaker, priority) => {
        speakInfo.speaker = speaker
        speakInfo.priority = priority
      })

      // 订阅各指令中更新终端节点状态事件
      bfglob.on('updateDeviceNodeTitle', this.updateTreeNode)
      // 同步树节点的增、删、改操作
      bfglob.on('addOneDeviceNode', this.addOneDeviceNode)
      bfglob.on('delOneDeviceNode', this.delOneDeviceNode)
      bfglob.on('updateOneDeviceNode', this.updateOneDeviceNode)
      bfglob.on('addOneOrgNode', this.addOneOrgNode)
      bfglob.on('delOneOrgNode', this.delOneOrgNode)
      bfglob.on('updateOneOrgNode', this.updateOneOrgNode)
      bfglob.on('treeSortChildren', this.treeSortChildren)

      //当终端更新/删除
      bfglob.on('update_global_deviceData', this.gotDbDeviceUpdate)

      //界面关闭按钮
      this.$nextTick(() => {
        const dialogEl = document.querySelector('.speaking-dialog .el-dialog__header')
        const closeBtn = dialogEl.querySelector('.el-dialog__close')
        closeBtn.addEventListener(
          'focus',
          () => {
            closeBtn.blur()
          },
          false
        )
      })
    },
    beforeUnmount() {
      bfglob.off('updateDeviceNodeTitle', this.updateTreeNode)
      bfglob.off('addOneDeviceNode', this.addOneDeviceNode)
      bfglob.off('delOneDeviceNode', this.delOneDeviceNode)
      bfglob.off('updateOneDeviceNode', this.updateOneDeviceNode)
      bfglob.off('addOneOrgNode', this.addOneOrgNode)
      bfglob.off('delOneOrgNode', this.delOneOrgNode)
      bfglob.off('updateOneOrgNode', this.updateOneOrgNode)

      bfglob.off('treeSortChildren', this.treeSortChildren)
      bfglob.off('update_global_deviceData', this.gotDbDeviceUpdate)
    },
  }
</script>

<style lang="scss">
  .el-dialog.speaking-dialog {
    width: 580px;
    .el-dialog__header {
      .el-dialog__title {
        font-size: 27px;
        font-family: 'AlibabaPuHuiTi2';
        background: linear-gradient(180deg, rgba(81, 224, 255, 0.52) 0%, rgba(81, 224, 255, 0.52) 22.66%, rgba(255, 255, 255, 0.52) 62.72%), #ffffff;
        -webkit-background-clip: text; /* 裁剪背景到文字 */
        -webkit-text-fill-color: transparent; /* 文字透明，让背景透出来 */
        text-shadow: 0px 0px 48px rgba(9, 171, 235, 0.61);
      }
    }
    .el-dialog__body {
      padding: 10px;
      display: flex;
      justify-content: center;
    }

    .el-form {
      overflow: hidden;
      position: relative;
      .el-form-item {
        margin-bottom: 8px;

        .el-form-item__label {
          height: 50px;

          .bf-form-item-button.el-button:hover {
            box-shadow: 0 0 0 var(--bf-border-size) var(--bf-form-item-btn-border-color) inset;
            color: #fff;
          }
        }
        .el-form-item__content {
          width: 380px;
        }

        .send-group-append.bf-input {
          border: none;
          border-radius: 0;
          box-shadow: 0 0 0 2px rgba(148, 204, 232, 1) inset;
          .el-input-group__prepend,
          .el-input-group__append {
            padding: 0;
            background-color: transparent;

            .target-input-btn {
              background-color: #1398e9;
              padding: 0;
              margin: 0;
              width: 30px;
              height: 30px;
              min-width: unset;

              &:last-child {
                margin-left: 12px;
              }
            }
          }
          .el-input-group__append {
            padding-right: 12px;
          }
          .el-input__wrapper {
            box-shadow: unset !important;
            input {
              text-align: center;
            }
          }
        }

        &.incoming-call {
          .el-form-item__content {
            display: flex;
            align-items: center;
          }

          .incoming-call-phoneNo {
            flex: auto;
          }

          .incoming-call-btns {
            flex: auto;
            min-width: 90px;
          }
        }

        &.speak-microphone {
          .el-form-item__content {
            display: block;
          }

          .animate-speak-box .animate-speak-btn {
            width: 76px;
            height: 100px;
            padding: 0;
            background-color: transparent;
            border: none;
          }

          .speak-prompt {
            line-height: 24px;
          }
        }

        .cancel-hang-icon,
        .usb-icon {
          width: 30px;
          height: 30px;
          background-color: #213f57;
          border-radius: 4px;
          margin: 0;
          margin-right: 10px;
          span {
            line-height: 30px;
          }
          &.usb-icon-active,
          &.cancel-hang-icon-active {
            background-color: #1398e9;
            span {
              color: #fff;
            }
          }
        }

        .join-key-dialing-btn {
          width: 50px;
          height: 50px;
          background-color: #053050;
          border-radius: 4px;
          line-height: 50px;
          text-align: center;
          position: absolute;
          margin: 0;
          bottom: 20px;
          right: 6px;
          cursor: pointer;

          span::before {
            font-size: 26px;
            color: #1398e9;
          }

          &:active {
            background-color: #ff801d;
            span::before {
              color: #fff;
            }
          }
        }
      }
    }
  }

  .el-dialog.speak-target-dialog {
    height: 60vh;

    .el-dialog__body {
      height: calc(100% - 45px);
    }
  }

  .el-dialog.listen-group-dialog {
    height: 60vh;

    .el-dialog__body {
      height: calc(100% - 45px);
    }
  }

  .notify-vnode-message {
    .footer {
      display: flex;
      justify-content: center;
      align-items: center;
      margin-top: 6px;

      .sender {
        flex: auto;
      }

      .confirm-button {
        flex: auto;
        flex-grow: 0;
      }
    }
  }

  // DialPad 相关样式
  .keypad-section {
    padding: 6px 6px 20px 6px;
    background: rgba(6, 121, 204, 0.46);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    position: relative;

    .keypad-grid {
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      gap: 12px;
    }

    .keypad-button {
      background: #065e9a;
      border: 2px solid #94cce8;
      border-radius: 5px;
      width: 115px;
      height: 60px;
      color: #94cce8;
      font-weight: 600;
      font-size: 32px;
      cursor: pointer;

      &:hover {
        background: rgba(148, 204, 232, 0.2);
        border-color: #fff;
      }

      &:active {
        background-color: #ff801d;
        color: #fff;
        border: 2px solid #94cce8;
      }
    }

    .join-net-call-btn {
      width: 50px;
      height: 50px;
      background-color: #053050;
      border-radius: 4px;
      line-height: 50px;
      text-align: center;
      position: absolute;
      margin: 0;
      bottom: 20px;
      right: 6px;
      cursor: pointer;

      span::before {
        font-size: 26px;
        color: #1398e9;
      }

      &:active {
        background-color: #ff801d;
        span::before {
          color: #fff;
        }
      }
    }
  }
</style>
